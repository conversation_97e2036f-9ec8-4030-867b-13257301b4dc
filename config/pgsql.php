<?php
// PostgreSQL specific configuration for <PERSON><PERSON>

return [
    'pgsql' => [
        'driver' => 'pgsql',
        'url' => env('DATABASE_URL'),
        'host' => env('DB_HOST', '127.0.0.1'),
        'port' => env('DB_PORT', '5433'),
        'database' => env('DB_DATABASE', 'laravel'),
        'username' => env('DB_USERNAME', 'postgres'),
        'password' => env('DB_PASSWORD', ''),
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
        
        // PostgreSQL specific settings
        'schema' => 'public',
        'options' => [
            // Enable prepared statements
            \PDO::ATTR_EMULATE_PREPARES => false,
        ],
        
        // For larger datasets, you might want to adjust these
        'pool' => [
            'min' => 2,
            'max' => 10,
        ],
    ],
];
