version: '3'
services:
  # PHP Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        user: laravel
        uid: 1000
    container_name: education-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - education-network
    depends_on:
      - db

  # Nginx Service
  webserver:
    image: nginx:alpine
    container_name: education-webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - education-network
    depends_on:
      - app

  # MySQL Service
  db:
    image: mysql:8.0
    container_name: education-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - education-mysql-data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - education-network

  # phpMyAdmin Service (optional)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: education-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
    networks:
      - education-network
    depends_on:
      - db

# Networks
networks:
  education-network:
    driver: bridge

# Volumes
volumes:
  education-mysql-data:
    driver: local
