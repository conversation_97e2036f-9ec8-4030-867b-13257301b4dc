<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class PostgreSQLTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test admin user
        User::create([
            'name' => 'PG Admin Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin', // Assuming your User model has a role field
        ]);

        // Log the success
        $this->command->info('PostgreSQL test user created successfully!');
    }
}
