[www]
user = laravel
group = laravel

listen = 0.0.0.0:9000

pm = dynamic
pm.max_children = 5
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3

; Make sure the FPM worker can access the session directory
php_admin_value[session.save_handler] = files
php_admin_value[session.save_path] = /var/www/storage/framework/sessions
php_admin_value[session.gc_probability] = 1
php_admin_value[session.gc_divisor] = 100
php_admin_value[session.gc_maxlifetime] = 7200

; Set proper permissions for session files
php_admin_flag[session.use_strict_mode] = on
php_admin_flag[session.use_cookies] = on
php_admin_flag[session.use_only_cookies] = on
php_admin_flag[session.cookie_httponly] = on
php_admin_value[session.cookie_samesite] = "Lax"

; Ensure proper error logging
php_admin_flag[log_errors] = on
php_admin_value[error_log] = /var/log/php-fpm/www-error.log
