# Guide d'installation avec Docker et MySQL

Ce guide détaillé vous aidera à configurer et exécuter la plateforme éducative en utilisant Docker avec MySQL comme base de données.

## Prérequis

- Docker (20.10+)
- Docker Compose (2.0+)
- Git

## Spécificités pour les utilisateurs Windows

Si vous utilisez Windows, voici quelques points importants à prendre en compte :

1. **Installation de Docker Desktop** :
   - Téléchargez et installez [Docker Desktop pour Windows](https://www.docker.com/products/docker-desktop)
   - Assurez-vous que WSL 2 (Windows Subsystem for Linux) est activé
   - Redémarrez votre ordinateur après l'installation

2. **Problèmes de chemins et de permissions** :
   - Utilisez des chemins avec des slashes avant (`/`) au lieu des backslashes (`\`)
   - Exécutez Docker Desktop en tant qu'administrateur si vous rencontrez des problèmes de permissions

3. **Problèmes de fin de ligne** :
   - Les scripts shell peuvent échouer à cause des fins de ligne Windows (CRLF)
   - Configurez Git pour utiliser LF au lieu de CRLF :
     ```bash
     git config --global core.autocrlf false
     ```
   - Ou convertissez manuellement les scripts shell :
     ```bash
     # Avec VS Code, changez le type de fin de ligne en LF
     # Ou utilisez dos2unix si disponible
     dos2unix docker-setup.sh
     ```

4. **Problèmes de performance** :
   - Évitez de placer le projet dans un dossier synchronisé (OneDrive, Dropbox)
   - Utilisez de préférence un dossier dans le système de fichiers WSL pour de meilleures performances

## Configuration initiale

1. Clonez le dépôt :
   ```bash
   git clone https://github.com/Karim-Benkhira/Plateforme-de-Formation-en-Ligne-Interactive.git
   cd Plateforme-de-Formation-en-Ligne-Interactive
   ```

2. Créez et configurez le fichier `.env` :
   ```bash
   cp .env.example .env
   ```

3. Modifiez les paramètres de base de données dans le fichier `.env` :
   ```
   DB_CONNECTION=mysql
   DB_HOST=db
   DB_PORT=3306
   DB_DATABASE=education
   DB_USERNAME=laravel
   DB_PASSWORD=StrongP@ssw0rd!
   ```

## Installation avec Docker

### Méthode 1 : Installation automatisée

Si le script d'installation automatisée est disponible :

```bash
chmod +x docker-setup.sh
./docker-setup.sh
```

### Méthode 2 : Installation manuelle étape par étape

1. Construire et démarrer les conteneurs Docker :
   ```bash
   docker-compose up -d --build
   ```

2. Installer les dépendances PHP :
   ```bash
   docker-compose exec app composer install
   ```

3. Générer la clé d'application :
   ```bash
   docker-compose exec app php artisan key:generate
   ```

4. Créer les dossiers nécessaires et définir les permissions :
   ```bash
   docker-compose exec app mkdir -p storage/framework/sessions storage/framework/views storage/framework/cache
   docker-compose exec app chmod -R 777 storage bootstrap/cache
   ```

5. Exécuter les migrations :
   ```bash
   docker-compose exec app php artisan migrate
   ```

6. Installer et compiler les assets frontend :
   ```bash
   docker-compose exec app npm install
   docker-compose exec app npm run build
   ```

7. Optimiser l'application :
   ```bash
   docker-compose exec app php artisan optimize:clear
   ```

## Accès à l'application

- Application web : http://localhost:8000
- phpMyAdmin : http://localhost:8081
  - Serveur : db
  - Utilisateur : laravel
  - Mot de passe : StrongP@ssw0rd!

## Résolution des problèmes courants

### Problème : Extension PDO MySQL manquante

**Symptôme** : Erreur "could not find driver" lors de la connexion à la base de données.

**Solution** : Assurez-vous que l'extension PDO MySQL est installée dans le Dockerfile :
```dockerfile
RUN docker-php-ext-install pdo pdo_mysql
```

Puis reconstruisez l'image Docker :
```bash
docker-compose up -d --build
```

### Problèmes spécifiques à Windows

#### Problème : Script shell non exécutable

**Symptôme** : Erreur "permission denied" ou "cannot execute" lors de l'exécution de `docker-setup.sh`.

**Solution** :
1. Convertissez les fins de ligne CRLF en LF :
   ```bash
   # Avec Git Bash
   dos2unix docker-setup.sh
   # Ou avec PowerShell
   (Get-Content docker-setup.sh) -replace "`r`n", "`n" | Set-Content -NoNewline docker-setup.sh
   ```

2. Exécutez le script avec Git Bash ou WSL :
   ```bash
   # Avec Git Bash
   bash docker-setup.sh
   # Ou avec WSL
   wsl bash docker-setup.sh
   ```

#### Problème : Erreur "The process cannot access the file because it is being used by another process"

**Symptôme** : Docker ne peut pas accéder à certains fichiers car ils sont verrouillés par Windows.

**Solution** :
1. Arrêtez tous les processus qui pourraient utiliser ces fichiers (éditeurs, terminaux)
2. Redémarrez Docker Desktop
3. Si le problème persiste, redémarrez votre ordinateur

### Problème : Erreurs de session

**Symptôme** : Erreurs liées aux sessions, comme "file_put_contents(): Failed to open stream: No such file or directory".

**Solution** : Créez les dossiers de sessions et définissez les permissions :
```bash
docker-compose exec app mkdir -p storage/framework/sessions
docker-compose exec app chmod -R 777 storage
```

### Problème : Erreur de connexion à la base de données

**Symptôme** : L'application ne peut pas se connecter à la base de données.

**Solution** :
1. Vérifiez que le conteneur de base de données est en cours d'exécution :
   ```bash
   docker-compose ps
   ```

2. Vérifiez les informations de connexion dans le fichier `.env`.

3. Essayez de vous connecter manuellement à la base de données :
   ```bash
   docker-compose exec db mysql -u laravel -p
   ```

4. Redémarrez les conteneurs :
   ```bash
   docker-compose restart
   ```

### Problème : Ports déjà utilisés

**Symptôme** : Erreur indiquant que les ports sont déjà utilisés lors du démarrage des conteneurs.

**Solution** : Modifiez les ports mappés dans le fichier `docker-compose.yml` :
```yaml
ports:
  - "8001:80"  # Changez 8000 en 8001 pour le serveur web
  - "8082:8080"  # Changez 8081 en 8082 pour phpMyAdmin
```

#### Problème spécifique à Windows : Erreur "Ports are not available"

**Symptôme** : Sous Windows, vous pouvez rencontrer des erreurs indiquant que les ports ne sont pas disponibles même si aucune application ne semble les utiliser.

**Solution** :
1. Vérifiez les ports utilisés avec PowerShell :
   ```powershell
   netstat -ano | findstr "8000"
   netstat -ano | findstr "8081"
   ```

2. Identifiez et arrêtez les processus qui utilisent ces ports :
   ```powershell
   # Remplacez PID par l'ID du processus trouvé avec netstat
   taskkill /F /PID PID
   ```

3. Si le problème persiste, utilisez des ports différents dans `docker-compose.yml`

## Commandes Docker utiles

```bash
# Voir les logs des conteneurs
docker-compose logs

# Voir les logs d'un conteneur spécifique
docker-compose logs app
docker-compose logs db

# Redémarrer tous les conteneurs
docker-compose restart

# Redémarrer un conteneur spécifique
docker-compose restart app

# Arrêter et supprimer tous les conteneurs
docker-compose down

# Arrêter et supprimer tous les conteneurs, volumes et images
docker-compose down -v --rmi all
```

## Maintenance

### Mettre à jour les dépendances

```bash
docker-compose exec app composer update
docker-compose exec app npm update
```

### Nettoyer le cache

```bash
docker-compose exec app php artisan optimize:clear
```

### Sauvegarder la base de données

```bash
docker-compose exec db mysqldump -u laravel -p education > backup.sql
```

### Restaurer la base de données

```bash
cat backup.sql | docker-compose exec -T db mysql -u laravel -p education
```
