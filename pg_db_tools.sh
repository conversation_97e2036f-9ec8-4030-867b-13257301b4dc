#!/bin/bash

# Script for PostgreSQL database backup and restore operations

BACKUP_DIR="./database/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/pg_backup_$TIMESTAMP.sql"

# Make backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Function to display help
show_help() {
    echo "PostgreSQL Database Backup and Restore Script"
    echo ""
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  backup    Create a new database backup"
    echo "  restore   Restore from a backup (interactive)"
    echo "  help      Display this help message"
    echo ""
}

# Function to create a backup
create_backup() {
    echo "Creating PostgreSQL database backup..."
    docker-compose exec db pg_dump -U ${DB_USERNAME:-postgres} -d ${DB_DATABASE:-laravel} > $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        echo "Backup created successfully: $BACKUP_FILE"
    else
        echo "Error creating backup!"
        exit 1
    fi
}

# Function to restore a backup
restore_backup() {
    # List available backups
    echo "Available backups:"
    ls -lh $BACKUP_DIR
    
    echo ""
    echo "Enter the backup file name to restore (without path):"
    read SELECTED_BACKUP
    
    if [ ! -f "$BACKUP_DIR/$SELECTED_BACKUP" ]; then
        echo "Error: Backup file not found!"
        exit 1
    fi
    
    echo "Warning: This will overwrite the current database. Continue? (y/n)"
    read CONFIRM
    
    if [ "$CONFIRM" != "y" ]; then
        echo "Restore cancelled."
        exit 0
    fi
    
    echo "Restoring from backup: $SELECTED_BACKUP"
    cat "$BACKUP_DIR/$SELECTED_BACKUP" | docker-compose exec -T db psql -U ${DB_USERNAME:-postgres} -d ${DB_DATABASE:-laravel}
    
    if [ $? -eq 0 ]; then
        echo "Database restored successfully!"
    else
        echo "Error restoring database!"
        exit 1
    fi
}

# Main script logic
case "$1" in
    backup)
        create_backup
        ;;
    restore)
        restore_backup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        ;;
esac

exit 0
