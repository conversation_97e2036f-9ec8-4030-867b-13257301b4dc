/**
 * CSRF Token Setup for AJAX Requests
 * 
 * This script sets up the CSRF token for all AJAX requests in the application.
 * It should be included in all pages that make AJAX requests.
 */

// Set up CSRF token for jQuery AJAX requests (if jQuery is used)
if (typeof $ !== 'undefined' || typeof jQuery !== 'undefined') {
    const jq = $ || jQuery;
    
    jq.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    });
}

// Set up CSRF token for fetch API requests
const originalFetch = window.fetch;
window.fetch = function(url, options = {}) {
    // Only add CSRF token to same-origin requests
    if (url.toString().startsWith(window.location.origin) || url.toString().startsWith('/')) {
        options = options || {};
        options.headers = options.headers || {};
        
        // Don't override if already set
        if (!options.headers['X-CSRF-TOKEN'] && !options.headers['x-csrf-token']) {
            const token = document.querySelector('meta[name="csrf-token"]');
            if (token) {
                options.headers['X-CSRF-TOKEN'] = token.content;
            }
        }
    }
    
    return originalFetch(url, options);
};

// Set up CSRF token for XMLHttpRequest
const originalXhrOpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function() {
    const method = arguments[0];
    const url = arguments[1];
    
    // Call the original open method
    originalXhrOpen.apply(this, arguments);
    
    // Only add CSRF token for non-GET requests to same origin
    if (method.toLowerCase() !== 'get' && 
        (url.toString().startsWith(window.location.origin) || 
         url.toString().startsWith('/'))) {
        
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            this.setRequestHeader('X-CSRF-TOKEN', token.content);
        }
    }
};

console.log('CSRF token setup complete for AJAX requests');
