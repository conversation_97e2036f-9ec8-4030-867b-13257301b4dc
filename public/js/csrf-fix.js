/**
 * CSRF Fix for Laravel Applications
 * 
 * This script helps fix the 419 PAGE EXPIRED error by:
 * 1. Adding CSRF token to all AJAX requests
 * 2. Refreshing the CSRF token periodically
 * 3. Adding event listeners to forms to ensure they have valid CSRF tokens
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the CSRF token from the meta tag
    let token = document.querySelector('meta[name="csrf-token"]');
    
    if (token) {
        // Set up CSRF token for all AJAX requests
        setupAjaxCsrf(token.content);
        
        // Add CSRF token to all forms that don't have it
        addCsrfToForms(token.content);
        
        // Refresh CSRF token every 30 minutes
        setInterval(function() {
            refreshCsrfToken();
        }, 30 * 60 * 1000); // 30 minutes
    } else {
        console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
    }
});

/**
 * Set up CSRF token for all AJAX requests
 */
function setupAjaxCsrf(token) {
    // For jQuery AJAX
    if (typeof $ !== 'undefined' || typeof jQuery !== 'undefined') {
        const jq = $ || jQuery;
        
        jq.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': token
            }
        });
    }
    
    // For Fetch API
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        // Only add CSRF token to same-origin requests
        if (url.toString().startsWith(window.location.origin) || url.toString().startsWith('/')) {
            options = options || {};
            options.headers = options.headers || {};
            
            // Don't override if already set
            if (!options.headers['X-CSRF-TOKEN'] && !options.headers['x-csrf-token']) {
                options.headers['X-CSRF-TOKEN'] = token;
            }
        }
        
        return originalFetch(url, options);
    };
    
    // For XMLHttpRequest
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
        const method = arguments[0];
        const url = arguments[1];
        
        // Call the original open method
        originalXhrOpen.apply(this, arguments);
        
        // Only add CSRF token for non-GET requests to same origin
        if (method.toLowerCase() !== 'get' && 
            (url.toString().startsWith(window.location.origin) || 
             url.toString().startsWith('/'))) {
            
            this.setRequestHeader('X-CSRF-TOKEN', token);
        }
    };
}

/**
 * Add CSRF token to all forms that don't have it
 */
function addCsrfToForms(token) {
    // Get all forms
    const forms = document.querySelectorAll('form');
    
    forms.forEach(function(form) {
        // Check if form already has CSRF token
        let hasCsrf = false;
        const inputs = form.querySelectorAll('input');
        
        inputs.forEach(function(input) {
            if (input.name === '_token') {
                hasCsrf = true;
            }
        });
        
        // If form doesn't have CSRF token, add it
        if (!hasCsrf) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = token;
            form.appendChild(csrfInput);
        }
    });
    
    // Add event listener to forms to ensure they have valid CSRF token
    document.addEventListener('submit', function(e) {
        const form = e.target;
        
        // Check if form has CSRF token
        let hasCsrf = false;
        const inputs = form.querySelectorAll('input');
        
        inputs.forEach(function(input) {
            if (input.name === '_token') {
                hasCsrf = true;
                // Update token value to latest
                input.value = document.querySelector('meta[name="csrf-token"]').content;
            }
        });
        
        // If form doesn't have CSRF token, add it
        if (!hasCsrf) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = document.querySelector('meta[name="csrf-token"]').content;
            form.appendChild(csrfInput);
        }
    });
}

/**
 * Refresh CSRF token
 */
function refreshCsrfToken() {
    // Create a new XMLHttpRequest
    const xhr = new XMLHttpRequest();
    
    // Configure it to get a new CSRF token
    xhr.open('GET', '/csrf-token', true);
    
    // Set up what happens on successful data submission
    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                
                if (response.token) {
                    // Update meta tag
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.content = response.token;
                    }
                    
                    // Update all forms
                    const tokenInputs = document.querySelectorAll('input[name="_token"]');
                    tokenInputs.forEach(function(input) {
                        input.value = response.token;
                    });
                    
                    // Update AJAX setup
                    setupAjaxCsrf(response.token);
                }
            } catch (e) {
                console.error('Error refreshing CSRF token:', e);
            }
        }
    };
    
    // Send the request
    xhr.send();
}
