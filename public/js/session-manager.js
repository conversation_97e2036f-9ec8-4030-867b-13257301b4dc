/**
 * Session Manager for Laravel Applications
 * 
 * This script helps manage sessions and CSRF tokens to prevent 419 PAGE EXPIRED errors.
 * It periodically checks the session status and refreshes the CSRF token if needed.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize session manager
    initSessionManager();
    
    // Add event listeners to forms
    setupFormListeners();
});

/**
 * Initialize the session manager
 */
function initSessionManager() {
    // Check session status immediately
    checkSessionStatus();
    
    // Set up periodic session checks (every 5 minutes)
    setInterval(function() {
        checkSessionStatus();
    }, 5 * 60 * 1000); // 5 minutes
}

/**
 * Check the session status
 */
function checkSessionStatus() {
    fetch('/check-session')
        .then(response => response.json())
        .then(data => {
            console.log('Session status:', data);
            
            // If session doesn't have a token, regenerate it
            if (!data.has_token) {
                regenerateSession();
            } else {
                // Update CSRF token in meta tag and forms
                updateCsrfToken(data.token);
            }
        })
        .catch(error => {
            console.error('Error checking session status:', error);
        });
}

/**
 * Regenerate the session
 */
function regenerateSession() {
    fetch('/regenerate-session')
        .then(response => response.json())
        .then(data => {
            console.log('Session regenerated:', data);
            
            // Update CSRF token in meta tag and forms
            updateCsrfToken(data.token);
        })
        .catch(error => {
            console.error('Error regenerating session:', error);
        });
}

/**
 * Update CSRF token in meta tag and forms
 */
function updateCsrfToken(token) {
    // Update meta tag
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag) {
        metaTag.content = token;
    } else {
        // Create meta tag if it doesn't exist
        const newMetaTag = document.createElement('meta');
        newMetaTag.name = 'csrf-token';
        newMetaTag.content = token;
        document.head.appendChild(newMetaTag);
    }
    
    // Update all forms
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        let tokenInput = form.querySelector('input[name="_token"]');
        
        if (tokenInput) {
            // Update existing token input
            tokenInput.value = token;
        } else {
            // Create new token input
            tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = token;
            form.appendChild(tokenInput);
        }
    });
    
    // Update AJAX setup for jQuery
    if (typeof $ !== 'undefined' || typeof jQuery !== 'undefined') {
        const jq = $ || jQuery;
        
        jq.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': token
            }
        });
    }
}

/**
 * Set up form submission listeners
 */
function setupFormListeners() {
    // Get all forms
    const forms = document.querySelectorAll('form');
    
    forms.forEach(function(form) {
        // Add submit event listener
        form.addEventListener('submit', function(event) {
            // Get the CSRF token from meta tag
            const token = document.querySelector('meta[name="csrf-token"]').content;
            
            // Get the token input in the form
            let tokenInput = form.querySelector('input[name="_token"]');
            
            // If token input doesn't exist, create it
            if (!tokenInput) {
                tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = '_token';
                form.appendChild(tokenInput);
            }
            
            // Update token value
            tokenInput.value = token;
        });
    });
}

// Refresh CSRF token when page becomes visible again
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // Page is now visible, check session status
        checkSessionStatus();
    }
});

// Refresh CSRF token before form submission
document.addEventListener('submit', function(event) {
    // Get the CSRF token from meta tag
    const token = document.querySelector('meta[name="csrf-token"]').content;
    
    // Get the form
    const form = event.target;
    
    // Get the token input in the form
    let tokenInput = form.querySelector('input[name="_token"]');
    
    // If token input doesn't exist, create it
    if (!tokenInput) {
        tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        form.appendChild(tokenInput);
    }
    
    // Update token value
    tokenInput.value = token;
});
