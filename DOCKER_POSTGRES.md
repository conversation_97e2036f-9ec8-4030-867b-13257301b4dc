## Installation avec Docker et PostgreSQL

Pour simplifier le déploiement et la configuration de la plateforme, nous utilisons Docker avec PostgreSQL comme base de données.

### Étapes d'installation

1. C<PERSON><PERSON> le dépôt :
   ```bash
   git clone https://github.com/votre-utilisateur/Plateforme-de-Formation-en-Ligne-Interactive.git
   cd Plateforme-de-Formation-en-Ligne-Interactive
   ```

2. <PERSON>z le script d'installation Docker :
   ```bash
   ./docker-setup.sh
   ```
   Ce script va :
   - Créer les dossiers nécessaires
   - Configurer le fichier .env
   - Construire et démarrer les conteneurs Docker
   - Installer les dépendances Composer et NPM
   - Exécuter les migrations et seeders
   - Configurer les liens de stockage

3. La plateforme sera accessible à :
   - Application web : http://localhost:8000
   - pgAdmin (gestion PostgreSQL) : http://localhost:5050

### Gestion de la base de données PostgreSQL

Pour faciliter la gestion de votre base de données, vous pouvez utiliser notre script d'utilitaires :

```bash
# Créer une sauvegarde
./pg_db_tools.sh backup

# Restaurer une sauvegarde
./pg_db_tools.sh restore
```

### Modification des variables d'environnement

Les principaux paramètres de la base de données PostgreSQL sont définis dans le fichier `.env` :

```
DB_CONNECTION=pgsql
DB_HOST=db
DB_PORT=5432
DB_DATABASE=laravel
DB_USERNAME=postgres
DB_PASSWORD=votre_mot_de_passe
```

> **Note**: Dans la configuration Docker, le port de PostgreSQL est 5432 à l'intérieur du réseau Docker, mais est mappé au port 5433 sur votre machine hôte pour éviter les conflits de port.

### Accès à pgAdmin

- URL : http://localhost:5050
- Email : <EMAIL> (ou la valeur définie dans PGADMIN_DEFAULT_EMAIL)
- Mot de passe : admin (ou la valeur définie dans PGADMIN_DEFAULT_PASSWORD)

Pour ajouter votre serveur dans pgAdmin :
1. Cliquez sur "Add New Server"
2. Nom : education-db
3. Onglet Connection :
   - Host : db
   - Port : 5433
   - Maintenance database : postgres
   - Username : postgres (ou la valeur définie dans DB_USERNAME)
   - Password : votre_mot_de_passe (ou la valeur définie dans DB_PASSWORD)
